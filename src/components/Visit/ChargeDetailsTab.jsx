import React, { useEffect, useState } from 'react'
import { Table, Button, Popover, Tag, Popconfirm, Form, Input } from 'antd'
import { useSelector } from 'react-redux'
import { useUI } from '../../common/UIProvider'
import {
  TABLE_COVERED_CHARGE_DETAILS_COLUMNS,
  TABLE_UNCOVERED_CHARGE_DETAILS_COLUMNS,
  PROCESSING_STATUS,
  TABLE_COVERED_CHARGE_DETAILS_CASHIER_COLUMNS,
  ACTION_VISIT_HISTORY,
} from './VisitConstant'
import COLOR from '../../common/color'
import { BUTTON_FONT_WEIGHT, FORM_MODE, MODE_VIEW_DATA } from '../../common/constant'
import PropTypes from '../../common/PropTypes'
import {
  getItemsService,
  patchMultiRecordDetails,
  syncVisitDetailChargeService,
  updateListItemService,
} from '../../common/services'
import lists from '../../common/lists'
import { MODULE_AUTH } from '../../store/auth'
import {
  calculateChargeDetails,
  calculateGroupTotals,
  groupChargeDetailsByItemGroup,
} from './VisitHelpers'
import {
  batchPromises,
  delay,
  displayCurrency,
  displayDateTime,
  handleError,
  logDebug,
  makeResizableColumns,
  patchItem,
  updateItem,
} from '../../common/helpers'
import EditVisitDetailChargePopup from './EditVisitDetailChargePopup'
import { MODULE_VISIT } from '../../store/Visit'
import AsyncButton from '../../common/components/AsyncButton'
import ResizableTitle from '../../common/components/ResizableTitle'
import { useVisitChargeDetails } from './hooks/useVisitChargeDetails'
import { usePatientVisit, VISIT_QUERY_KEYS } from './hooks/usePatientVisit'
import { useQueryClient } from '@tanstack/react-query'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import { useRunOnNewValues } from '../../common/hooks/useRunOnNewValue'
import dayjs from '../../common/dayjs'
import { VISIT_CHARGE_HISTORY_SNAPSHOT } from './ChargeDetail/ChargeDetailConstant'
import { PERMISSION } from '../Auth/AuthConstant'
import { useAuth } from '../../common/AuthProvider'
import { usePolicySubscription } from './hooks/usePolicySubscription'
import useApp from 'antd/es/app/useApp'
import { useForm } from 'antd/es/form/Form'
import useDeepCompareEffect from 'use-deep-compare-effect'
import { useAllChargeDetails } from '../../queryHooks/useAllChargeDetails'
import { processAndExportData } from '../Tool/PrintChargeDetail'

const propTypes = {
  mainVisit: PropTypes.object,
  selectedTabKey: PropTypes.string,
  currentPatientVisitMappingViews: PropTypes.array,
  selectedPatientVisitMappingViews: PropTypes.array,
  onSave: PropTypes.func,
  setIsVisiblePolicySubscriptionPopup: PropTypes.func,
}

const ChargeDetailsTab = ({
  mainVisit,
  selectedTabKey,
  selectedPatientVisitMappingViews,
  onSave,
  setIsVisiblePolicySubscriptionPopup,
}) => {
  // hooks
  const ui = useUI()
  const { checkPermission } = useAuth()
  const queryClient = useQueryClient()
  const app = useApp()
  // state
  const [coveredVisitChargeDetails, setCoveredVisitChargeDetails] = useState([])
  const [uncoveredVisitChargeDetails, setUncoveredVisitChargeDetails] = useState([])
  const [totalPrices, setTotalPrices] = useState({})
  const { currentUser, modeViewData, isDebugMode } = useSelector((state) => state[MODULE_AUTH])
  const systemReferralDispositionRefList = useSelector(
    (state) => state[MODULE_VISIT].systemReferralDispositionRefList,
  )

  const currentApiPatientVisitMedicalCodingViewList = useSelector(
    (state) => state[MODULE_VISIT].currentApiPatientVisitMedicalCodingViewList,
  )
  const [editingRecord, setEditingRecord] = useState(null)
  const [isPopupVisible, setIsPopupVisible] = useState(false)
  const [expandedCoveredKeys, setExpandedCoveredKeys] = useState([])
  const [expandedUncoveredKeys, setExpandedUncoveredKeys] = useState([])
  const { addPatientVisitHistory, refetchHistory } = usePatientVisitHistory()
  const {
    data: { currentPatientVisit, syncVisitChargeStatus, healthInsuranceCards },
    startFastRefetch,
    refetchPatientVisit,
  } = usePatientVisit(selectedPatientVisitMappingViews[0]?.patient_visit_id)
  const isSyncing = syncVisitChargeStatus === 'SYNCING'

  const [formModal] = useForm()

  const {
    data: rawChargeDetailsData,
    refetchData: refetchChargeDetails,
    moveChargeDetailQuery,
    setVisitChargeDetailsDraft,
    isLoading: isLoadingChargeDetails,
    isSuccess: isSuccessChargeDetails,
    isFetching: isFetchingChargeDetails,
  } = useVisitChargeDetails(currentPatientVisit)
  const { coveredChargeDetails: allCoveredChargeDetails } = useAllChargeDetails(
    mainVisit?.patient_visit_id,
    selectedTabKey === '3',
  )

  // exclude extended items
  const visitChargeDetailsData = rawChargeDetailsData.filter((item) => !item.extended_item_flag)

  const { suggestedPolicySubscriptions } = usePolicySubscription({
    mainVisit,
    visitChargeDetailsData: visitChargeDetailsData || [],
    healthInsuranceCards,
    selectedPatientVisitMappingViews,
  })

  const [coveredChargeCols, setCoveredChargeCols] = useState([
    ...TABLE_COVERED_CHARGE_DETAILS_COLUMNS,
  ])
  const [uncoveredChargeCols, setUncoveredChargeCols] = useState([
    ...TABLE_UNCOVERED_CHARGE_DETAILS_COLUMNS,
  ])
  const [isExpandedCoverdTable, setIsExpandedCoverdTable] = useState(false)

  const [isExpandedUncoverdTable, setIsExpandedUncoverdTable] = useState(false)
  const [draftMovedChargeList, setDraftMovedChargeList] = useState([])

  //store
  const { visitDetailMode } = useSelector((state) => state[MODULE_VISIT])

  // variables
  const mergedPatientVisitId = currentPatientVisit?.patient_visit_id

  const defaultDisabled =
    ((visitDetailMode === FORM_MODE.view ||
      mainVisit?.processing_status === PROCESSING_STATUS.SENT_TO_GATEWAY.name_e) &&
      !checkPermission(PERMISSION.SIO_MANAGER)) ||
    isFetchingChargeDetails

  // effect suggestedPolicySubscriptions changed and have different value
  useEffect(() => {
    if (
      suggestedPolicySubscriptions.length > 0 &&
      suggestedPolicySubscriptions[0]?.name_e != mainVisit?.policy_name
    ) {
      setIsVisiblePolicySubscriptionPopup(true)
    }
  }, [suggestedPolicySubscriptions])

  useDeepCompareEffect(() => {
    // Always process the data, even if it's empty
    // This ensures we clear previous data when visitChargeDetailsData is empty
    if (visitChargeDetailsData.length > 0) {
      // Filter out items with extended_item_flag=true for calculations
      const filteredChargeDetails = visitChargeDetailsData.filter(
        (item) => !item.extended_item_flag,
      )

      // Calculate totals using filtered data
      let total = calculateGroupTotals(filteredChargeDetails, [
        'total_after_tax',
        'medication_management_fee',
      ])

      // medication_management_fee includes all price_difference > 0
      total.medication_management_fee += filteredChargeDetails
        .filter((item) => item.manual_ss_cover_flag)
        .reduce((sum, item) => sum + (item.price_difference > 0 ? item.price_difference : 0), 0)

      setTotalPrices(total)
    } else {
      // Reset totals when data is empty
      setTotalPrices({
        total_after_tax: 0,
        medication_management_fee: 0,
      })
    }

    handleSetChargeDetailDataSource(visitChargeDetailsData, allCoveredChargeDetails)
  }, [visitChargeDetailsData, allCoveredChargeDetails, draftMovedChargeList])

  const [syncAllChargeFlag, setSyncAllChargeFlag] = useState(false) // run first time => true
  useDeepCompareEffect(() => {
    let interval

    if (
      !isLoadingChargeDetails &&
      !isSyncing &&
      !!currentPatientVisit?.patient_visit_id &&
      !syncAllChargeFlag &&
      isSuccessChargeDetails
    ) {
      if (visitChargeDetailsData.length === 0) {
        logDebug('SYNC ALL CHARGE!!!')
        handleSyncAllChargeDetails() // null => sync all
      } else {
        handleSyncAllChargeDetails({
          byLastUpdated: Math.max(
            ...visitChargeDetailsData.map((item) => new Date(item.charge_detail_lu_updated)),
          ),
        })
      }

      // auto sync every 15 minutes
      interval = setInterval(
        () => {
          handleSyncAllChargeDetails({
            byLastUpdated: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          })
        },
        15 * 60 * 1000,
      )

      setSyncAllChargeFlag(true)
    }

    return () => clearInterval(interval)
  }, [
    isLoadingChargeDetails,
    isSyncing,
    visitChargeDetailsData,
    currentPatientVisit?.patient_visit_id,
    syncAllChargeFlag,
    isSuccessChargeDetails,
  ])

  // When component mounted and run one time for one patient_visit_id
  useRunOnNewValues(
    (pvId, tabKey) => {
      // run only when pvId and tabKey has value
      if (tabKey !== '3') {
        return
      }

      const interval = setInterval(
        () => {
          syncVisitDetailChargeService(
            pvId,
            currentUser.User_id,
            new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          )
        },
        15 * 60 * 1000,
      )

      return () => clearInterval(interval)
    },
    [currentPatientVisit?.patient_visit_id, selectedTabKey],
  )

  // refetchChargeDetails when status changed
  useEffect(() => {
    if (syncVisitChargeStatus && !isSyncing) {
      refetchChargeDetails()
    }
  }, [syncVisitChargeStatus])

  useEffect(() => {
    if (modeViewData === MODE_VIEW_DATA.CASHIER) {
      setCoveredChargeCols(TABLE_COVERED_CHARGE_DETAILS_CASHIER_COLUMNS)
    } else {
      setCoveredChargeCols(TABLE_COVERED_CHARGE_DETAILS_COLUMNS)
    }
  }, [modeViewData])

  const mappingChargeError = (chargeList = [], pAllCoveredChargeDetails) => {
    return chargeList.map((record) => {
      if (record.serviced_date_time === null) {
        return {
          ...record,
          highlightClass: 'highlight-row-blue',
          error: 'Chưa có ngày thực hiện',
        }
      }

      if (
        record?.purchased_price > 0 &&
        Math.round(record?.purchased_price) < Math.round(record?.unit_price) &&
        (record?.medicine_id || record?.medical_supplies_id)
      ) {
        return {
          ...record,
          highlightClass: 'highlight-row-red',
          error: 'Giá mua thấp hơn giá bán',
        }
      }

      // find allCoveredChargeDetails less than or equal record.NGAY_YL
      if (
        pAllCoveredChargeDetails?.some((item) => {
          return (
            item.NGAY_YL <= record.NGAY_YL &&
            record.ss_item_group_rcd == '13' &&
            record.charge_detail_id !== item.charge_detail_id &&
            record.ss_item_code === item.ss_item_code
          )
        })
      ) {
        return {
          ...record,
          highlightClass: 'highlight-row-red',
          error: 'Dịch vụ khám đã được charge trước đó',
        }
      }

      return record
    })
  }

  const handleSetChargeDetailDataSource = async (
    visitChargeDetails,
    pAllCoveredChargeDetails = [],
  ) => {
    if (!visitChargeDetails || visitChargeDetails.length === 0) {
      // Clear all data when visitChargeDetails is empty
      setCoveredVisitChargeDetails([])
      setUncoveredVisitChargeDetails([])
      return
    }

    const mappingVisitDatasetDetail = visitChargeDetails.map((item) => ({
      ...item,
      visit_dataset_detail: `${currentPatientVisit?.visit_code} - ${displayDateTime(
        currentPatientVisit?.actual_visit_datetime,
      )}`,
    }))

    // Filter out items with extended_item_flag=true for display in ChargeDetailsTab
    const filteredVisitDatasetDetail = mappingVisitDatasetDetail.filter(
      (item) => !item.extended_item_flag,
    )

    let coveredVisitChargeDetails = filteredVisitDatasetDetail.filter(
      (item) => item.manual_ss_cover_flag == true,
    )

    coveredVisitChargeDetails = mappingChargeError(
      coveredVisitChargeDetails,
      pAllCoveredChargeDetails,
    )

    const uncoveredVisitChargeDetails = filteredVisitDatasetDetail.filter(
      (item) => item.manual_ss_cover_flag === false,
    )

    setCoveredVisitChargeDetails(groupChargeDetailsByItemGroup(coveredVisitChargeDetails, true))

    setUncoveredVisitChargeDetails(
      groupChargeDetailsByItemGroup(uncoveredVisitChargeDetails, false),
    )
  }

  const handleRowEdit = (record) => {
    setEditingRecord(record)
    setIsPopupVisible(true)
  }

  const handleClose = () => {
    setIsPopupVisible(false)
  }

  const handleSyncAllChargeDetails = async ({ byLastUpdated } = { byLastUpdated: null }) => {
    // Immediately update the sync status - use the same as setState
    queryClient.setQueryData(
      [VISIT_QUERY_KEYS.PATIENT_VISIT, currentPatientVisit?.patient_visit_id],
      {
        ...currentPatientVisit,
        sync_visit_charge_status: 'SYNCING',
      }, // Can be {} or (oldData) => ({})
    )

    // Call the sync service
    await syncVisitDetailChargeService(
      currentPatientVisit?.patient_visit_id,
      currentUser.User_id,
      byLastUpdated ? dayjs(byLastUpdated).toISOString() : null,
    )
    // Refetch patientVisit and toggle fast refetching 2s
    await startFastRefetch()

    // for sync very fast
    await delay(5000)
    await startFastRefetch()
  }

  const expandAllCovered = () => {
    const allParentKeys = coveredVisitChargeDetails
      .filter((record) => record.children?.length > 0)
      .map((record) => record.key)
    setExpandedCoveredKeys(allParentKeys)
  }

  const collapseAllCovered = () => {
    setExpandedCoveredKeys([])
  }

  useEffect(() => {
    expandAllCovered()
  }, [coveredVisitChargeDetails])

  const expandAllUncovered = () => {
    const allParentKeys = uncoveredVisitChargeDetails
      .filter((record) => record.children?.length > 0)
      .map((record) => record.key)
    setExpandedUncoveredKeys(allParentKeys)
  }

  const collapseAllUncovered = () => {
    setExpandedUncoveredKeys([])
  }

  useEffect(() => {
    expandAllUncovered()
  }, [uncoveredVisitChargeDetails])

  const handleDisableSendCashier = () =>
    !visitChargeDetailsData.some((item) => !item.ar_invoice_id || !item.last_sent_cashier_date_time)

  const handleSetDraftMovedChargeList = (record, ssCoverFlag) => {
    setDraftMovedChargeList((prev) =>
      patchItem(prev, {
        ...record,
        manual_ss_cover_flag: ssCoverFlag,
        last_sent_cashier_date_time: null,
        last_sent_cashier_user_id: null,
      }),
    )
  }

  const handleMoveUp = (record) => {
    handleSetDraftMovedChargeList(record, true)

    const associatedExtendedItems = rawChargeDetailsData.filter(
      (item) => item.extended_item_flag && item.charge_detail_id === record.charge_detail_id,
    )
    associatedExtendedItems.forEach((r) => handleSetDraftMovedChargeList(r, true))
  }

  const handleMoveDown = (record) => {
    handleSetDraftMovedChargeList(record, false)

    const associatedExtendedItems = rawChargeDetailsData.filter(
      (item) => item.extended_item_flag && item.charge_detail_id === record.charge_detail_id,
    )
    associatedExtendedItems.forEach((r) => handleSetDraftMovedChargeList(r, false))
  }

  const handleSaveMoves = async () => {
    try {
      // history
      await batchPromises(
        draftMovedChargeList.map((newRecord) => async () => {
          await addPatientVisitHistory.mutateAsync({
            historyData: {
              patient_visit_id: mainVisit?.patient_visit_id,
              merged_patient_visit_id: mergedPatientVisitId,
              note: '',
              data_snapshot: VISIT_CHARGE_HISTORY_SNAPSHOT[
                newRecord.manual_ss_cover_flag
                  ? ACTION_VISIT_HISTORY.MOVE_UP_CHARGE_DETAIL
                  : ACTION_VISIT_HISTORY.MOVE_DOWN_CHARGE_DETAIL
              ].makeSnapshot(newRecord.ss_item_code),
            },
            action: newRecord.manual_ss_cover_flag
              ? ACTION_VISIT_HISTORY.MOVE_UP_CHARGE_DETAIL
              : ACTION_VISIT_HISTORY.MOVE_DOWN_CHARGE_DETAIL,
          })
        }),
      )

      await patchMultiRecordDetails(
        lists.visit_charge_detail,
        draftMovedChargeList.map((newRecord) => ({
          visit_charge_detail_id: newRecord.visit_charge_detail_id,
          charge_detail_id: newRecord.charge_detail_id,
          manual_ss_cover_flag: newRecord.manual_ss_cover_flag,
          last_sent_cashier_date_time: newRecord.last_sent_cashier_date_time,
          last_sent_cashier_user_id: newRecord.last_sent_cashier_user_id,
        })),
      )

      // Xóa danh sách pending moves sau khi hoàn thành
      setDraftMovedChargeList([])
      await refetchChargeDetails()
      ui.notiSuccess('Đã lưu các thao tác di chuyển thành công')
    } catch (error) {
      handleError(error)
    }
  }

  // Add this useEffect to monitor sync status changes
  useEffect(() => {
    const handleStatusChange = async () => {
      const hasCoveredCharges = visitChargeDetailsData.some(
        (r) =>
          r.manual_ss_cover_flag === true &&
          !r.last_sent_cashier_date_time &&
          !r.extended_item_flag,
      )
      if (hasCoveredCharges) {
        await updateListItemService(lists.patient_visit, mainVisit?.patient_visit_id, {
          processing_status: PROCESSING_STATUS.WAITING_BHYT.name_e,
        })

        // add to history
        await addPatientVisitHistory.mutateAsync({
          historyData: {
            patient_visit_id: mainVisit?.patient_visit_id,
            merged_patient_visit_id: mergedPatientVisitId,
            note: 'Có charge mới chưa được nhân viên BHYT xử lý, vui lòng quay lại sau!',
          },
          action: ACTION_VISIT_HISTORY.SAVE_INFO,
        })

        refetchPatientVisit()

        ui.notiWarning('Có charge mới chưa được nhân viên BHYT xử lý, vui lòng quay lại sau!')
      }
    }

    if (
      syncVisitChargeStatus &&
      !isSyncing &&
      modeViewData === MODE_VIEW_DATA.CASHIER &&
      currentPatientVisit?.processing_status === PROCESSING_STATUS.WAITING_CASHIER.name_e
    ) {
      handleStatusChange()
    }
  }, [currentPatientVisit?.sync_visit_charge_status, isSyncing, visitChargeDetailsData])

  return (
    <div className="container-fluid">
      <div
        className="row mt-2"
        style={{ color: COLOR.red, fontWeight: BUTTON_FONT_WEIGHT, fontSize: '18px' }}>
        <div className="d-flex justify-content-between">
          <div>{mainVisit?.policy_name}</div> {/*Truc doi xai mainVisit de hien thi muc huong*/}
          <div> TỔNG SỐ TIỀN SAU THUẾ: {displayCurrency(totalPrices.total_after_tax)}</div>
          <div>
            <span style={{ textDecoration: 'underline' }}>MEDICATION MANAGEMENT FEE: </span>
            {displayCurrency(totalPrices.medication_management_fee)}
          </div>
        </div>
      </div>
      {/* <Button
        onClick={async () => {
          const processRecords = visitChargeDetails.map((record) => ({
            patient_visit_id: record.patient_visit_id,
            charge_detail_id: record.charge_detail_id,
            manual_ss_covered_item_flag: record?.ss_covered_item_flag,
            lu_user_id: currentUser.User_id
          }))
          await patchMultiRecordDetails(lists.visit_charge_detail.listName, processRecords)
        }}>
        Process Records
      </Button> */}

      {/* <Button
        onClick={() => {
          const result = groupChargeDetailsByItemGroup(visitChargeDetails)
        }}>
        GROUP DATA
      </Button> */}

      <div
        className="mt-2 fw-bold d-flex justify-content-between align-items-center"
        style={{ color: COLOR.cyan, fontWeight: BUTTON_FONT_WEIGHT }}>
        <div className="d-flex align-items-center gap-2">
          <Button
            size="small"
            icon={<i className="fa-solid fa-plus" />}
            onClick={expandAllCovered}
          />
          <Button
            size="small"
            icon={<i className="fa-solid fa-minus" />}
            onClick={collapseAllCovered}
          />
          1. Danh sách item nằm trong Bảo hiểm
          <Tag>
            {currentPatientVisit?.visit_code} -{' '}
            {displayDateTime(currentPatientVisit?.actual_visit_datetime)}
          </Tag>
          <Button
            hidden={!isDebugMode}
            onClick={() => {
              logDebug('coveredVisitChargeDetails', coveredVisitChargeDetails)
              calculateChargeDetails(visitChargeDetailsData)
            }}
            size="small"
            className="ms-1">
            Calculate
          </Button>
        </div>

        <div className="d-flex gap-2">
          <AsyncButton
            disabled={defaultDisabled || draftMovedChargeList.length === 0}
            onClick={() => setDraftMovedChargeList([])}
            icon={<i className="fa fa-close" />}>
            HỦY LƯU THAO TÁC MOVE
          </AsyncButton>

          <AsyncButton
            disabled={defaultDisabled || draftMovedChargeList.length === 0}
            onClick={handleSaveMoves}
            variant="solid"
            color="green"
            icon={<i className="fa fa-save" />}>
            LƯU THAO TÁC MOVE ({draftMovedChargeList.length})
          </AsyncButton>

          <Button
            icon={
              isExpandedCoverdTable ? (
                <i className="ms-1 fa-solid fa-down-left-and-up-right-to-center"></i>
              ) : (
                <i className="ms-1 fa-solid fa-up-right-and-down-left-from-center"></i>
              )
            } // zoom icon
            onClick={() => setIsExpandedCoverdTable(!isExpandedCoverdTable)}></Button>
        </div>
      </div>
      <Table
        size="small"
        bordered
        className="custom-table mt-2"
        expandable={{
          expandedRowKeys: expandedCoveredKeys,
          onExpandedRowsChange: (keys) => setExpandedCoveredKeys(keys),
        }}
        scroll={{
          x: coveredChargeCols.map((c) => c.width).reduce((a, b) => a + b) + 100,
          y: isExpandedCoverdTable ? '70vh' : '37vh',
        }}
        rowClassName={(record) => {
          if (record?.children) {
            return 'highlight-row-gray'
          }

          // if (record.serviced_date_time === null) {
          //   return 'highlight-row-blue'
          // }

          // if (
          //   record?.purchased_price > 0 &&
          //   Math.round(record?.purchased_price) < Math.round(record?.unit_price) &&
          //   (record?.medicine_id || record?.medical_supplies_id)
          // ) {
          //   return 'highlight-row-red'
          // }

          // // find allCoveredChargeDetails less than or equal record.NGAY_YL
          // if (
          //   allCoveredChargeDetails?.some(
          //     (item) =>
          //       item.NGAY_YL <= record.NGAY_YL &&
          //       // record.ss_item_group_rcd == '13' &&
          //       record.charge_detail_id !== item.charge_detail_id &&
          //       record.ss_item_code === item.ss_item_code,
          //   )
          // ) {
          //   return 'highlight-row-red'
          // }

          return record?.highlightClass
        }}
        components={{ header: { cell: ResizableTitle } }}
        columns={[
          ...makeResizableColumns(
            coveredChargeCols.map((col) => {
              if (col.key === 'health_insurance_name') {
                return {
                  ...col,
                  render: (text, record) => {
                    // OPD: hide medicine
                    // IPD: hide medicine, no care VTYT because OPD do not have VTYT
                    const hide =
                      record.children ||
                      ((record?.medicine_id !== null || modeViewData === MODE_VIEW_DATA.CASHIER) &&
                        !checkPermission(PERMISSION.SIO_MANAGER))

                    const hasDraftMove = draftMovedChargeList.find(
                      (d) => d.visit_charge_detail_id === record.visit_charge_detail_id,
                    )

                    return hide ? (
                      text
                    ) : (
                      <span>
                        <Popover
                          content={
                            hasDraftMove
                              ? 'Nhấn LƯU THAO TÁC MOVE để cập nhật và tính toán lại'
                              : 'Move up'
                          }>
                          <AsyncButton
                            disabled={defaultDisabled || hasDraftMove}
                            variant="solid"
                            color="orange"
                            className="me-1"
                            size="small"
                            onClick={() => handleMoveDown(record)}
                            icon={<i className="fa fa-arrow-down ms-1" />}></AsyncButton>
                        </Popover>
                        {text}
                      </span>
                    )
                  },
                }
              }

              return { ...col }
            }),
            setCoveredChargeCols,
            coveredVisitChargeDetails,
          ),
          {
            title: ' ',
            dataIndex: 'operation',
            key: 'operation',
            fixed: 'right',
            width: 50,
            align: 'right',
            render: (_, record) => {
              return (
                !record.children && (
                  <div className="d-flex justify-content-end gap-2">
                    <Button
                      disabled={defaultDisabled}
                      hidden={modeViewData === MODE_VIEW_DATA.CASHIER}
                      size="small"
                      icon={<i className="fa fa-edit ms-1" />}
                      onClick={() => {
                        handleRowEdit(record)
                      }}></Button>
                  </div>
                )
              )
            },
          },
        ]}
        dataSource={coveredVisitChargeDetails.sort((a, b) => parseFloat(a.key) - parseFloat(b.key))}
        pagination={false}
        summary={(pageData) => {
          const totalFields = coveredChargeCols.map((col) => col.dataIndex)
          if (modeViewData === MODE_VIEW_DATA.CASHIER) {
            const { total_after_tax, ss_cover } = calculateGroupTotals(pageData, totalFields)

            return (
              <Table.Summary.Row className="highlight-row sticky-summary">
                <Table.Summary.Cell colSpan={5}>Tổng cộng</Table.Summary.Cell>
                <Table.Summary.Cell colSpan={1} align="right">
                  {displayCurrency(total_after_tax)}
                </Table.Summary.Cell>
                <Table.Summary.Cell colSpan={1} align="right"></Table.Summary.Cell>
                <Table.Summary.Cell colSpan={1} align="right"></Table.Summary.Cell>
                <Table.Summary.Cell colSpan={1} align="right">
                  {displayCurrency(ss_cover)}
                </Table.Summary.Cell>
                <Table.Summary.Cell colSpan={1} align="right"></Table.Summary.Cell>
                <Table.Summary.Cell colSpan={1} align="right"></Table.Summary.Cell>
                <Table.Summary.Cell colSpan={1} align="right"></Table.Summary.Cell>
                <Table.Summary.Cell colSpan={1} align="right"></Table.Summary.Cell>
              </Table.Summary.Row>
            )
          } else {
            const {
              //total_before_tax,
              total_after_tax,
              health_insurance_amount,
              ss_cover,
              BN_CUNG_CHI_TRA,
              BN_TU_CHI_TRA,
              TT_BN_CHI_TRA,
            } = calculateGroupTotals(pageData, totalFields)

            return (
              <Table.Summary.Row className="highlight-row sticky-summary">
                <Table.Summary.Cell colSpan={14}>Tổng cộng</Table.Summary.Cell>
                <Table.Summary.Cell colSpan={1} align="right">
                  {displayCurrency(total_after_tax)}
                </Table.Summary.Cell>
                <Table.Summary.Cell colSpan={1} align="right"></Table.Summary.Cell>

                <Table.Summary.Cell colSpan={1} align="right">
                  {displayCurrency(health_insurance_amount)}
                </Table.Summary.Cell>
                <Table.Summary.Cell align="right">{displayCurrency(ss_cover)}</Table.Summary.Cell>
                <Table.Summary.Cell align="right">
                  {displayCurrency(BN_CUNG_CHI_TRA)}
                </Table.Summary.Cell>
                <Table.Summary.Cell align="right">
                  {displayCurrency(BN_TU_CHI_TRA)}
                </Table.Summary.Cell>
                <Table.Summary.Cell align="right">
                  {displayCurrency(TT_BN_CHI_TRA)}
                </Table.Summary.Cell>

                <Table.Summary.Cell colSpan={100}></Table.Summary.Cell>
              </Table.Summary.Row>
            )
          }
        }}
      />

      {modeViewData !== MODE_VIEW_DATA.CASHIER && (
        <div>
          <div className="d-flex justify-content-between align-items-center mt-2">
            <div
              className="fw-bold d-flex align-items-center gap-2"
              style={{ color: COLOR.cyan, fontWeight: BUTTON_FONT_WEIGHT }}>
              <Button
                size="small"
                icon={<i className="fa-solid fa-plus" />}
                onClick={expandAllUncovered}
              />
              <Button
                size="small"
                icon={<i className="fa-solid fa-minus" />}
                onClick={collapseAllUncovered}
              />
              2. Danh sách item nằm ngoài Bảo hiểm
            </div>
            <div>
              <Button
                icon={
                  isExpandedCoverdTable ? (
                    <i className="ms-1 fa-solid fa-down-left-and-up-right-to-center"></i>
                  ) : (
                    <i className="ms-1 fa-solid fa-up-right-and-down-left-from-center"></i>
                  )
                } // zoom icon
                onClick={() => setIsExpandedUncoverdTable(!isExpandedUncoverdTable)}></Button>
            </div>
          </div>
          <Table
            expandable={{
              expandedRowKeys: expandedUncoveredKeys,
              onExpandedRowsChange: (keys) => setExpandedUncoveredKeys(keys),
            }}
            scroll={{
              x: uncoveredChargeCols.map((c) => c.width).reduce((a, b) => a + b) + 100,
              y: isExpandedUncoverdTable ? '70vh' : '15vh',
            }}
            components={{ header: { cell: ResizableTitle } }}
            bordered
            size="small"
            className="custom-table mt-2"
            columns={[
              ...makeResizableColumns(
                uncoveredChargeCols.map((col) => {
                  if (col.key === 'title_group_name') {
                    return {
                      ...col,
                      render: (text, record) => {
                        // OPD: hide medicine
                        // IPD: hide medicine
                        const hide =
                          record.children ||
                          ((record?.medicine_id !== null ||
                            record?.ss_optional_cover_flag === false) &&
                            !checkPermission(PERMISSION.SIO_MANAGER))

                        const hasDraftMove = draftMovedChargeList.find(
                          (d) => d.visit_charge_detail_id === record.visit_charge_detail_id,
                        )

                        return hide ? (
                          text
                        ) : (
                          <span>
                            <Popover
                              content={
                                hasDraftMove
                                  ? 'Nhấn LƯU THAO TÁC MOVE để cập nhật và tính toán lại'
                                  : 'Move up'
                              }>
                              <AsyncButton
                                disabled={defaultDisabled || hasDraftMove}
                                variant="solid"
                                color="orange"
                                className="me-1"
                                size="small"
                                onClick={() => handleMoveUp(record)}
                                icon={<i className="fa fa-arrow-up ms-1" />}></AsyncButton>
                            </Popover>
                            {text}
                          </span>
                        )
                      },
                    }
                  }

                  return { ...col }
                }),
                setUncoveredChargeCols,
                uncoveredVisitChargeDetails,
              ),
              {
                title: ' ',
                dataIndex: 'operation',
                key: 'operation',
                fixed: 'right',
                align: 'right',
                className: 'bg-white',
                render: () => {
                  return ''
                },
              },
            ]} // Add width to the columns
            dataSource={uncoveredVisitChargeDetails}
            pagination={false}
            rowClassName={(record) => (record.ss_optional_cover_flag ? 'highlight-row' : '')}
            summary={(pageData) => {
              const totalFields = ['total_after_tax']
              const { total_after_tax } = calculateGroupTotals(pageData, totalFields)

              return (
                <Table.Summary.Row className="highlight-row sticky-summary">
                  <Table.Summary.Cell colSpan={6}>Tổng cộng</Table.Summary.Cell>
                  <Table.Summary.Cell align="right" colSpan={1}>
                    {displayCurrency(total_after_tax)}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell colSpan={999}></Table.Summary.Cell>
                </Table.Summary.Row>
              )
            }}
          />
        </div>
      )}

      <div className="row sticky-bottom bg-white pt-3 pb-1">
        <div className="d-flex justify-content-end align-items-center gap-2">
          <div className="d-flex flex-column align-items-right">
            <div>
              Trạng thái đồng bộ:{' '}
              <span style={{ color: COLOR.red, fontWeight: BUTTON_FONT_WEIGHT }}>
                {syncVisitChargeStatus || 'N/A'}
              </span>
            </div>
            <div className="text-xs">
              Charge mới nhất lúc:{' '}
              {visitChargeDetailsData.length > 0
                ? displayDateTime(
                    Math.max(
                      ...visitChargeDetailsData.map(
                        (item) => new Date(item.charge_detail_lu_updated),
                      ),
                    ),
                  )
                : 'N/A'}
            </div>
          </div>

          <AsyncButton
            hidden={modeViewData === MODE_VIEW_DATA.CASHIER || visitChargeDetailsData.length === 0}
            onClick={refetchChargeDetails}>
            LÀM MỚI
          </AsyncButton>

          <AsyncButton
            hidden={modeViewData === MODE_VIEW_DATA.CASHIER || visitChargeDetailsData.length === 0}
            disabled={defaultDisabled}
            onClick={async () =>
              await handleSyncAllChargeDetails({
                byLastUpdated: Math.max(
                  ...visitChargeDetailsData.map((item) => new Date(item.charge_detail_lu_updated)),
                ),
              })
            }>
            THỬ LẠI
          </AsyncButton>

          <Popover
            content={`Đồng bộ từ ${
              displayDateTime(
                Math.max(
                  ...visitChargeDetailsData.map((item) => new Date(item.charge_detail_lu_updated)),
                ),
              ) || 'N/A'
            } đến hiện tại`}>
            <AsyncButton
              hidden={
                modeViewData === MODE_VIEW_DATA.CASHIER || visitChargeDetailsData.length === 0
              }
              disabled={defaultDisabled}
              icon={<i className="fas fa-sync-alt"></i>}
              loading={isSyncing}
              variant="solid"
              color="cyan"
              onClick={async () =>
                await handleSyncAllChargeDetails({
                  byLastUpdated: Math.max(
                    ...visitChargeDetailsData.map(
                      (item) => new Date(item.charge_detail_lu_updated),
                    ),
                  ),
                })
              }>
              ĐỒNG BỘ GẦN NHẤT
            </AsyncButton>
          </Popover>
          <Popover content="Đồng bộ tất cả charge từ ORION">
            <Popconfirm
              title="Xác nhận đồng bộ tất cả?"
              description="Bạn có thể thử bấm ĐỒNG BỘ GẦN NHẤT trước khi bấm ĐỒNG BỘ TẤT CẢ!"
              okText="Xác nhận đồng bộ tất cả"
              cancelText="Đóng"
              onConfirm={async () => await handleSyncAllChargeDetails()}>
              <AsyncButton
                hidden={modeViewData === MODE_VIEW_DATA.CASHIER}
                disabled={defaultDisabled}
                icon={<i className="fas fa-sync-alt"></i>}
                loading={isSyncing}
                danger>
                ĐỒNG BỘ TẤT CẢ
              </AsyncButton>
            </Popconfirm>
          </Popover>
          <AsyncButton
            hidden={modeViewData === MODE_VIEW_DATA.CASHIER}
            disabled={defaultDisabled}
            loading={isSyncing}
            onClick={async () => {
              app.modal.confirm({
                title: 'Ghi chú gửi Cashier',
                content: (
                  <Form form={formModal} layout="vertical" style={{ width: '100%' }}>
                    <Form.Item name="note" label="Ghi chú">
                      <Input.TextArea
                        autoSize={{ minRows: 2 }}
                        placeholder="Nhập ghi chú..."
                        rows={3}
                      />
                    </Form.Item>
                  </Form>
                ),
                onOk: async () => {
                  try {
                    const noteValue = formModal.getFieldValue('note')

                    // update charge details to DB
                    const itemsToSend = coveredVisitChargeDetails
                      .reduce((acc, item) => [...acc, ...item.children], []) // children only
                      .map((v) => ({
                        ...v,
                        last_sent_cashier_user_id: currentUser?.User_id,
                        last_sent_cashier_date_time: dayjs(),
                      }))
                    const totalSent = itemsToSend.reduce((acc, item) => acc + item.ss_cover, 0)

                    await patchMultiRecordDetails(lists.visit_charge_detail, itemsToSend)

                    await updateListItemService(lists.patient_visit, mainVisit?.patient_visit_id, {
                      processing_status: PROCESSING_STATUS.WAITING_CASHIER.name_e,
                    })

                    await updateListItemService(
                      lists.patient_visit,
                      selectedPatientVisitMappingViews[0]?.patient_visit_id,
                      {
                        sent_to_cashier_by: currentUser?.User_id,
                      },
                    )

                    //lưu vào lịch sử
                    const historyData = {
                      patient_visit_id: mainVisit?.patient_visit_id,
                      merged_patient_visit_id: mergedPatientVisitId,
                      note: noteValue, // Add the note here
                      data_snapshot: VISIT_CHARGE_HISTORY_SNAPSHOT[
                        ACTION_VISIT_HISTORY.SEND_TO_CASHIER
                      ].makeSnapshot(totalSent, itemsToSend),
                    }

                    addPatientVisitHistory.mutateAsync({
                      historyData,
                      action: ACTION_VISIT_HISTORY.SEND_TO_CASHIER,
                    })

                    // Refresh data
                    await onSave()
                    await refetchChargeDetails()

                    // refetch main visit
                    queryClient.invalidateQueries({
                      queryKey: [VISIT_QUERY_KEYS.PATIENT_VISIT, mainVisit?.patient_visit_id],
                    })

                    refetchHistory()

                    // reset formModal
                    formModal.resetFields()
                    ui.notiSuccess('Send to cashier successfully')
                  } catch (error) {
                    handleError(error)
                  }
                },
                destroyOnClose: true,
                okText: 'Xác nhận',
                cancelText: 'Hủy',
                maskClosable: true,
              })
            }}
            icon={<i className="fa fa-paper-plane" />}
            variant="solid"
            color="blue">
            SEND TO CASHIER
          </AsyncButton>

          <AsyncButton
            hidden={
              mainVisit?.visit_type_group_rcd !== 'IPD' ||
              [PROCESSING_STATUS.WAITING_BHYT.name_e].includes(mainVisit?.processing_status)
            }
            variant="solid"
            color="green"
            icon={<i className="fa-solid fa-file-lines"></i>}
            disabled={coveredVisitChargeDetails.length === 0}
            onClick={async () => {
              let selectedChildren = coveredVisitChargeDetails

              if (selectedChildren.length === 0 || selectedChildren[0]?.children.length === 0) {
                ui.notiWarning('Hóa đơn chưa có dữ liệu BHYT')
                return
              }

              const firstChild = selectedChildren[0]?.children?.[0]

              let sentCashierBy = {}
              if (firstChild?.last_sent_cashier_user_id) {
                sentCashierBy = await getItemsService(lists.employee_dataset, {
                  filter: `user_id eq ${firstChild?.last_sent_cashier_user_id}`,
                  top: 1,
                })
                sentCashierBy = sentCashierBy?.value?.[0] || {}
              }
              let invoiceCreateBy = {}
              if (firstChild?.created_by_user_name) {
                invoiceCreateBy = await getItemsService(lists.employee_dataset, {
                  filter: `user_name eq '${firstChild?.created_by_user_name}'`,
                  top: 1,
                })
                invoiceCreateBy = invoiceCreateBy?.value?.[0] || {}
              }

              const currentUserFullName = currentUser?.Employee_name || ''
              const sentCashierFullName = sentCashierBy?.employee_name || currentUserFullName
              const invoiceCreateFullName = invoiceCreateBy?.employee_name || currentUserFullName

              // Get the invoice creation date from the first child
              // This is important for determining the end date in the billing statement
              const invoiceCreateOn = firstChild?.created_on_date_time || dayjs().toISOString()

              processAndExportData(
                selectedChildren,
                mainVisit,
                healthInsuranceCards,
                systemReferralDispositionRefList,
                currentApiPatientVisitMedicalCodingViewList,
                sentCashierFullName,
                currentUserFullName,
                invoiceCreateFullName,
                invoiceCreateOn,
                // Pass a fresh copy of the selected visits to ensure we're not using stale data
                [...selectedPatientVisitMappingViews],
              )

              addPatientVisitHistory.mutateAsync({
                historyData: {
                  patient_visit_id: mainVisit?.patient_visit_id,
                  merged_patient_visit_id: mergedPatientVisitId,
                },
                action: ACTION_VISIT_HISTORY.PRINT_CHARGE,
              })
            }}>
            In bảng kê charge
          </AsyncButton>
        </div>
      </div>
      {editingRecord && (
        <EditVisitDetailChargePopup
          visible={isPopupVisible}
          onClose={handleClose}
          onSave={async () => {
            await refetchChargeDetails()
            setIsPopupVisible(false)
          }}
          record={editingRecord}
          visitChargeDetails={visitChargeDetailsData}
          visitTypeGroup={mainVisit?.visit_type_group_rcd}
        />
      )}
    </div>
  )
}

ChargeDetailsTab.propTypes = propTypes

export default ChargeDetailsTab
