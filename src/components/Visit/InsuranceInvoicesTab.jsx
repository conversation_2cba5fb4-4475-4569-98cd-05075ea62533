import { Table } from 'antd/lib'
import React, { useCallback, useEffect, useState } from 'react'
import { ACTION_VISIT_HISTORY, getInvoiceCols, PROCESSING_STATUS } from './VisitConstant'
import { useSelector } from 'react-redux'
import { useUI } from '../../common/UIProvider'
import { Button, Dropdown, Popover, Select, Tag } from 'antd'
import {
  getAllApiArInvoiceDetailViewsByPatientVisitId,
  getSSTable1sByPatientVisitId,
} from './VisitService'
import { checkValidInvoiceRow, groupDataByInvoiceTransaction } from './VisitHelpers'
import COLOR from '../../common/color'
import { BUTTON_FONT_WEIGHT, FORM_MODE, MODE_VIEW_DATA } from '../../common/constant'
import { generateXMLTableByInvoices } from '../Tool/XmlToolService'
import PropTypes from '../../common/PropTypes'
import {
  getItemsService,
  patchMultiRecordDetails,
  updateListItemService,
} from '../../common/services'
import lists from '../../common/lists'
import AsyncButton from '../../common/components/AsyncButton'
import {
  batchGetItemsByColumnName,
  handleError,
  logDebug,
  makeResizableColumns,
} from '../../common/helpers'
import { MODULE_AUTH } from '../../store/auth'
import { processAndExportData } from '../Tool/PrintChargeDetail'
import { MODULE_VISIT } from '../../store/Visit'
import ResizableTitle from '../../common/components/ResizableTitle'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import { usePatientVisit } from './hooks/usePatientVisit'
import _, { isEmpty } from 'lodash'
import { VISIT_CHARGE_HISTORY_SNAPSHOT } from './ChargeDetail/ChargeDetailConstant'
import useApp from 'antd/es/app/useApp'
import { PERMISSION } from '../Auth/AuthConstant'
import { useAuth } from '../../common/AuthProvider'
import config from '../../common/config'
import { RULE_WARNING_STATUS } from './RuleManagement/RuleManagementConstant'

const propTypes = {
  mainVisit: PropTypes.object,
  selectedTabKey: PropTypes.string,
  selectedPatientVisitMappingViews: PropTypes.array,
  onSave: PropTypes.func,
  currentPatientVisitMappingViews: PropTypes.array,
  isRefreshOnline: PropTypes.bool,
  setIsRefreshOnline: PropTypes.func,
  setSelectedTabKey: PropTypes.func,
}

const InsuranceInvoicesTab = ({
  mainVisit,
  selectedTabKey,
  selectedPatientVisitMappingViews,
  onSave,
  currentPatientVisitMappingViews,
  isRefreshOnline,
  setIsRefreshOnline,
  setSelectedTabKey,
}) => {
  //hooks
  const { currentUser, modeViewData, isDebugMode } = useSelector((state) => state[MODULE_AUTH])

  const isAdmin = currentUser?.User_name === 'xyz100871'
  const { addPatientVisitHistory, refetchHistory } = usePatientVisitHistory()
  const ui = useUI()
  const app = useApp()
  const { checkPermission } = useAuth()

  const {
    data: { healthInsuranceCards },
    refetchPatientVisit,
  } = usePatientVisit(mainVisit?.patient_visit_id)

  const { visitHistoryData } = usePatientVisitHistory(mainVisit?.patient_visit_id)

  const systemReferralDispositionRefList = useSelector(
    (state) => state[MODULE_VISIT].systemReferralDispositionRefList,
  )

  const currentApiPatientVisitMedicalCodingViewList = useSelector(
    (state) => state[MODULE_VISIT].currentApiPatientVisitMedicalCodingViewList,
  )

  const { visitDetailMode } = useSelector((state) => state[MODULE_VISIT])

  //state
  const [loading, setLoading] = useState(true)
  const [visitsApiArInvoiceDetails, setVisitsApiArInvoiceDetails] = useState([]) // Raw API Data
  const [selectedVisitsInvoiceData, setSelectedVisitsInvoiceData] = useState([]) // Grouped Data
  const [medicalTreatmentTypes, setMedicalTreatmentTypes] = useState([])
  const [selectedInsuranceInvoiceRowKeys, setSelectedInsuranceInvoiceRowKeys] = useState([])
  const [selectedInvoiceRows, setSelectedInvoiceRows] = useState([])
  const [medicalTreatmentInvoices, setMedicalTreatmentInvoices] = useState([])
  const [invoiceCols, setInvoiceCols] = useState(
    getInvoiceCols(
      _,
      medicalTreatmentInvoices,
      setMedicalTreatmentInvoices,
      setSelectedVisitsInvoiceData,
      isAdmin,
      selectedPatientVisitMappingViews,
    ),
  )
  const [filterMedicalTreatmentType, setFilterMedicalTreatmentType] = useState('')
  const [isIgnoreValidateMoveUpItems, setIsIgnoreValidateMoveUpItems] = useState(false)

  const defaultDisabled =
    (visitDetailMode === FORM_MODE.view ||
      mainVisit?.processing_status === PROCESSING_STATUS.SENT_TO_GATEWAY.name_e) &&
    !checkPermission(PERMISSION.SIO_MANAGER)

  const handleGetMedicalTreatmentInvoices = async (invoiceNoList = []) => {
    if (!invoiceNoList[0]) {
      return []
    }

    try {
      const medicalTreatmentInvoices = await batchGetItemsByColumnName(
        [...new Set(invoiceNoList)],
        'invoice_no',
        lists.medical_treatment_invoice_mapping,
      )

      // await getItemsService(
      //   lists.medical_treatment_invoice_mapping,
      //   {
      //     filter: `invoice_no in (${invoiceNoList // [...new Set(invoiceNoList)]
      //       .map((invoiceNo) => `'${invoiceNo}'`)
      //       .join(',')}) and active_flag eq true`,
      //   },
      // ).then((res) => res?.value || [])

      setMedicalTreatmentInvoices(medicalTreatmentInvoices)

      return medicalTreatmentInvoices
    } catch (error) {
      handleError(error, 'InsuranceInvoicesTab>handleGetMedicalTreatmentInvoices')
    }
  }

  const getInitData = async () => {
    setLoading(true)
    try {
      ui.setLoading(true)
      let api_ar_invoice_detail_views = []
      // const isRefreshedData = checkisRefreshOnline()

      const promises = currentPatientVisitMappingViews.map((element) =>
        getAllApiArInvoiceDetailViewsByPatientVisitId(element?.patient_visit_id),
      )
      const results = await Promise.all(promises)
      const medicalTreatmentTypes = await getItemsService(lists.medical_treatment_type_ref, {
        filter: `active_flag eq true`,
        orderBy: `MA_LOAI_KCB`,
      }).then((res) => res?.value || [])

      results.forEach((data) => {
        api_ar_invoice_detail_views.push(...data.value)
      })
      const medicalTreatmentInvoices = await handleGetMedicalTreatmentInvoices(
        api_ar_invoice_detail_views.map((item) => item.ar_invoice_transaction_text),
      )

      // Store the raw API data
      setVisitsApiArInvoiceDetails(api_ar_invoice_detail_views)
      setInvoiceCols(
        getInvoiceCols(
          medicalTreatmentTypes,
          medicalTreatmentInvoices,
          setMedicalTreatmentInvoices,
          setSelectedVisitsInvoiceData,
          isAdmin,
          selectedPatientVisitMappingViews,
        ),
      )
      setMedicalTreatmentTypes(medicalTreatmentTypes)

      ui.setLoading(false)
    } catch (error) {
      handleError(error, 'InsuranceInvoicesTab>getInitData')
    } finally {
      setIsRefreshOnline(false)
      setLoading(false)
    }
  }

  // listen selectedTabKey
  useEffect(() => {
    if (selectedTabKey === '4') {
      refetchHistory()
    }
  }, [selectedTabKey])

  // update from effect instead of manual in function
  useEffect(() => {
    setSelectedInvoiceRows(
      selectedInsuranceInvoiceRowKeys.map((key) =>
        selectedVisitsInvoiceData.find((item) => item.key === key),
      ),
    )
  }, [selectedInsuranceInvoiceRowKeys, selectedVisitsInvoiceData])

  const handleGroupDataByInvoiceTransaction = () => {
    let data = visitsApiArInvoiceDetails.filter((element) =>
      selectedPatientVisitMappingViews.some(
        (item) => item.patient_visit_id === element.patient_visit_id,
      ),
    )

    if (isIgnoreValidateMoveUpItems) {
      data = data.filter((item) => item.manual_ss_cover_flag)
    }

    // check gross_amount > 0 but not manual_ss_cover_flag
    data = data.filter((item) => {
      if (item.gross_amount > 0 && !item.manual_ss_cover_flag) {
        // check if item_id exist with exact minus value, then return false
        const minusItem = data.find(
          (i) => i.item_id === item.item_id && i.gross_amount === -item.gross_amount,
        )

        if (minusItem) {
          return false
        }

        return true
      }

      return true
    })

    // only gross_amount > 0
    data = data.filter((item) => item.gross_amount > 0)

    // final group the data
    let groupData = groupDataByInvoiceTransaction(data)

    // mapping last sent to cashier
    groupData = groupData.map((group) => {
      let lastHistory = visitHistoryData.find(
        (history) =>
          history.action === ACTION_VISIT_HISTORY.SEND_TO_CASHIER &&
          group.patient_visit_id ===
            (history?.merged_patient_visit_id || history?.patient_visit_id),
      )

      const dataSnapshot = VISIT_CHARGE_HISTORY_SNAPSHOT[
        ACTION_VISIT_HISTORY.SEND_TO_CASHIER
      ].getSnapshot(lastHistory?.data_snapshot)

      return {
        ...group,
        lastHistory: {
          ...lastHistory,
          dataSnapshot,
        },
      }
    })

    // add checkValidRow
    groupData = groupData.map((group) => ({ ...group, isValidRow: checkValidInvoiceRow(group) }))

    setSelectedVisitsInvoiceData(groupData)

    return groupData
  }

  useEffect(() => {
    handleGroupDataByInvoiceTransaction(visitsApiArInvoiceDetails)
  }, [visitsApiArInvoiceDetails, visitHistoryData, isIgnoreValidateMoveUpItems])

  const handleSaveInvoiceMapping = useCallback(async () => {
    ui.setLoading(true)
    await patchMultiRecordDetails(
      lists.medical_treatment_invoice_mapping,
      medicalTreatmentInvoices.map((item) => {
        return { ...item, lu_user_id: currentUser?.User_id }
      }),
    )

    ui.setLoading(false)
  }, [medicalTreatmentInvoices, currentUser])

  // Helper function to check if visit has any invoices
  const hasInvoices = () => {
    return visitsApiArInvoiceDetails.some((item) => item.gross_amount > 0)
  }

  const isCashierAlreadyConfirmed = () => {
    // check all visitHistoryData and all selectedPatientVisitMappingViews
    // all selectedPatientVisitMappingViews already confirmed by cashier
    return selectedPatientVisitMappingViews.every((visit) => {
      return visitHistoryData.some(
        (history) =>
          history.action === ACTION_VISIT_HISTORY.CASHIER_CONFIRM &&
          (visit.patient_visit_id === history.patient_visit_id ||
            visit.patient_visit_id === history.merged_patient_visit_id),
      )
    })
  }

  const handleCashierConfirm = async () => {
    try {
      // Update the processing status to WAITING_MS
      await updateListItemService(lists.patient_visit, mainVisit.patient_visit_id, {
        processing_status: PROCESSING_STATUS.WAITING_MS.name_e,
      })

      await updateListItemService(
        lists.patient_visit,
        selectedPatientVisitMappingViews[0]?.patient_visit_id,
        {
          cashier_confirmed_by: currentUser?.User_id,
        },
      )

      // Add to patient visit history
      await addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: mainVisit?.patient_visit_id,
          merged_patient_visit_id: selectedPatientVisitMappingViews[0]?.patient_visit_id,
        },
        action: ACTION_VISIT_HISTORY.CASHIER_CONFIRM,
      })

      // Refresh data
      refetchPatientVisit()

      ui.notiSuccess('Cashier xác nhận thành công')
    } catch (error) {
      handleError(error, 'InsuranceInvoicesTab>cashierConfirm')
      ui.notiError('Có lỗi xảy ra khi xác nhận')
    }
  }

  useEffect(() => {
    if (selectedTabKey === '4') {
      getInitData()
    }
  }, [selectedTabKey, selectedPatientVisitMappingViews])

  /**
   *
   * @param {*} filterInvoiceNo ['FVH-22222', 'FVH-22223']
   * @returns
   */
  const handleGenerateXMLTable = async (filterInvoiceNo = []) => {
    filterInvoiceNo = filterInvoiceNo.map((invoicNo) => {
      const findItem = visitsApiArInvoiceDetails.find(
        (inv) => inv.ar_invoice_transaction_text === invoicNo,
      )

      return {
        invoice_no: invoicNo,
        patient_visit_id: findItem?.patient_visit_id,
        created_on_date_time: findItem?.created_on_date_time,
      }
    })

    filterInvoiceNo = filterInvoiceNo.sort(
      (a, b) => new Date(a.created_on_date_time) - new Date(b.created_on_date_time),
    )

    // mainVisit go first if exist
    if (filterInvoiceNo.find((item) => item.patient_visit_id === mainVisit?.patient_visit_id)) {
      filterInvoiceNo = filterInvoiceNo.sort((a, b) => {
        if (a.patient_visit_id === mainVisit?.patient_visit_id) {
          return -1
        }
        if (b.patient_visit_id === mainVisit?.patient_visit_id) {
          return 1
        }
        return 0
      })
    }

    const filterHN = mainVisit?.visible_patient_id
    await generateXMLTableByInvoices(
      {
        filterInvoiceNo: filterInvoiceNo.map((item) => item.invoice_no),
        filterHN,
        patientVisitId: mainVisit?.patient_visit_id,
      },
      ui,
    )
  }

  const validateInvoice = async (invoiceList = selectedInvoiceRows) => {
    if (!selectedVisitsInvoiceData[0].isValidRow && !isAdmin) {
      ui.notiError('Hóa đơn không hợp lệ')
      return false
    }

    // Add check for PP_VO_CAM - only for IPD
    const invalidPPVOCAM = []
    invoiceList.forEach((invoice) => {
      invoice.children?.forEach((item) => {
        if (
          item?.technical_services_id &&
          (item?.ss_item_group_rcd == '8' || item?.ss_item_group_rcd == '18') &&
          !item.PP_VO_CAM &&
          mainVisit?.visit_type_group_rcd === 'IPD'
        ) {
          invalidPPVOCAM.push(item)
        }
      })
    })

    if (invalidPPVOCAM.length > 0) {
      ui.notiError(
        'Có item chưa có PP_VO_CAM',
        invalidPPVOCAM.map((item) => (
          <div key={item.key}>
            Nhóm {item.ss_item_group_rcd}, {item.item_code}: {item.item_name_e}
          </div>
        )),
      )
      return false
    }
    //validate KET_LUAN
    const NEEDKETLUANGROUP = '2'
    const invalidInvoices = selectedInvoiceRows
      .map((invoice) => {
        const invalidItems = invoice.children?.filter(
          (item) => item.ss_item_group_rcd == NEEDKETLUANGROUP && isEmpty(item.KET_LUAN),
        )
        return invalidItems?.length > 0 ? { invoice, invalidItems } : null
      })
      .filter(Boolean)

    if (invalidInvoices.length > 0) {
      const errorMessages = invalidInvoices.map(
        ({ invoice, invalidItems }) =>
          `Invoice : ${invoice.key} => Invalid Items: [${invalidItems
            .map((item) => `${item.item_code}`)
            .join(', ')}]`,
      )
      ui.notiError(`Các hoá đơn sau có item chưa có kết luận:\n${errorMessages.join('\n')}`)
      return false
    }

    // final, confirm if exist xml

    const ssTable1 = await getSSTable1sByPatientVisitId(mainVisit.patient_visit_id)
    if (ssTable1?.value?.length > 0) {
      // if exist, confirm before proceed
      const isConfirmed = await ui.showConfirm({
        title: 'Xác nhận',
        content:
          'Bảng XML đã tồn tại, bạn có muốn tiếp tục tạo mới? Thao tác này sẽ "Hủy bảng XML" cũ và tạo lại bảng XML mới',
      })

      if (!isConfirmed) {
        return false
      }

      // Tracking Delete XML if exist
      addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: mainVisit?.patient_visit_id,
          note: 'MA_LK: ' + ssTable1?.value[0]?.MA_LK,
        },
        action: ACTION_VISIT_HISTORY.DELETE_XML,
      })
    }

    return true
  }

  const handleValidateBeforeCreateXML = async () => {
    if (!selectedInvoiceRows[0]) {
      ui.notiError('Vui lòng chọn hóa đơn')
      return
    }

    const isValid = await validateInvoice(selectedInvoiceRows)
    if (!isValid) {
      return
    }

    // save invovoice mapping
    await handleSaveInvoiceMapping()

    await updateListItemService(lists.patient_visit, mainVisit.patient_visit_id, {
      processing_status: PROCESSING_STATUS.WAITING_XML_CREATION.name_e,
      warning_status: RULE_WARNING_STATUS.NEW.key,
    })

    //lưu vào lịch sử
    addPatientVisitHistory.mutateAsync({
      historyData: {
        patient_visit_id: mainVisit?.patient_visit_id,
        note: selectedPatientVisitMappingViews[0]?.note,
      },
      action: ACTION_VISIT_HISTORY.CREATE_XML,
    })

    await handleGenerateXMLTable(selectedInsuranceInvoiceRowKeys)

    setSelectedTabKey('5')
  }

  const handlePrintInvoice = async () => {
    let selectedChildren = selectedInvoiceRows

    if (selectedChildren.length === 0 || selectedChildren[0]?.children.length === 0) {
      ui.notiWarning('Hóa đơn chưa có dữ liệu BHYT')
      return
    }

    const firstChild = selectedChildren[0]?.children?.[0]

    let sentCashierBy = {}
    if (firstChild?.last_sent_cashier_user_id) {
      sentCashierBy = await getItemsService(lists.employee_dataset, {
        filter: `user_id eq ${firstChild?.last_sent_cashier_user_id}`,
        top: 1,
      })
      sentCashierBy = sentCashierBy?.value?.[0] || {}
    }
    let invoiceCreateBy = {}
    if (firstChild?.created_by_user_name) {
      invoiceCreateBy = await getItemsService(lists.employee_dataset, {
        filter: `user_name eq '${firstChild?.created_by_user_name}'`,
        top: 1,
      })
      invoiceCreateBy = invoiceCreateBy?.value?.[0] || {}
    }

    const sentCashierFullName = sentCashierBy?.employee_name || ''
    const currentUserFullName = currentUser?.Employee_name || ''
    const invoiceCreateFullName = invoiceCreateBy?.employee_name || ''

    // Get the invoice creation date from the first child
    // This is important for determining the end date in the billing statement
    const invoiceCreateOn = firstChild?.created_on_date_time || ''

    processAndExportData(
      selectedChildren,
      mainVisit,
      healthInsuranceCards,
      systemReferralDispositionRefList,
      currentApiPatientVisitMedicalCodingViewList,
      sentCashierFullName,
      currentUserFullName,
      invoiceCreateFullName,
      invoiceCreateOn,
      // Pass a fresh copy of the selected visits to ensure we're not using stale data
      [...selectedPatientVisitMappingViews],
    )

    addPatientVisitHistory.mutateAsync({
      historyData: {
        patient_visit_id: mainVisit?.patient_visit_id,
        note: selectedPatientVisitMappingViews[0]?.note,
      },
      action: ACTION_VISIT_HISTORY.PRINT_INVOICE,
    })
  }

  const handleSIOConfirm = async () => {
    try {
      // check all invoice, no need to select MA_LOAI_KCB == '02'
      if (!selectedVisitsInvoiceData[0]) {
        ui.notiWarning('Chưa có hóa đơn')
        return
      }

      let autoInvoice = selectedVisitsInvoiceData.filter((inv) => inv.MA_LOAI_KCB === '02')

      if (!autoInvoice.length) {
        ui.notiWarning('Chưa có hóa đơn KCB loại 02')
        return
      }

      const isValid = await validateInvoice(autoInvoice)
      if (!isValid) {
        return
      }

      // save invovoice mapping
      await handleSaveInvoiceMapping()

      await updateListItemService(lists.patient_visit, mainVisit.patient_visit_id, {
        processing_status: PROCESSING_STATUS.WAITING_XML_CREATION.name_e,
        warning_status: RULE_WARNING_STATUS.NEW.key,
      })

      //lưu vào lịch sử
      addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: mainVisit?.patient_visit_id,
          note: autoInvoice.map((item) => item.id).join(';'),
        },
        action: ACTION_VISIT_HISTORY.CREATE_XML,
      })
      addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: mainVisit?.patient_visit_id,
          note: autoInvoice.map((item) => item.id).join(';'),
        },
        action: ACTION_VISIT_HISTORY.SIO_CONFIRM,
      })

      await handleGenerateXMLTable(autoInvoice.map((item) => item.id))

      setSelectedTabKey('5')
    } catch (error) {
      handleError(error, 'InsuranceInvoicesTab>handleSIOConfirm')
    }
  }

  if (loading) {
    return <div>Loading...</div>
  }

  return (
    <div className="container-fluid">
      <div className="flex flex-wrap justify-between items-center mb-2 ">
        <span>
          mã visit: {selectedPatientVisitMappingViews[0]?.patient_visit_id}
          __{selectedTabKey} <br></br>
          invoice: _{selectedInsuranceInvoiceRowKeys.map((item) => item).join(',')}
        </span>
        {/* <div>
          <Checkbox
            checked={isIgnoreValidateMoveUpItems}
            onChange={(e) => {
              setIsIgnoreValidateMoveUpItems(e.target.checked)
            }}>
            Bỏ qua những dòng không nằm trong hóa đơn
          </Checkbox>
        </div> */}
        <div className="flex gap-2 items-center ">
          <AsyncButton
            hidden={modeViewData === MODE_VIEW_DATA.CASHIER}
            disabled={defaultDisabled}
            onClick={handleSaveInvoiceMapping}
            icon={<i className="fa-solid fa-save"></i>}
            variant="solid"
            color="primary">
            Lưu thông tin hóa đơn
          </AsyncButton>
          <span>Mã loại KCB :</span>
          <Select
            optionLabelProp="label"
            allowClear
            className="flex-1 min-w-[250px]"
            placeholder="Chọn loại KCB"
            onChange={(_, option) => setFilterMedicalTreatmentType(option?.item?.MA_LOAI_KCB)}>
            {medicalTreatmentTypes.map((item) => (
              <Select.Option
                key={item.MA_LOAI_KCB}
                value={item.MA_LOAI_KCB}
                item={item}
                label={`Loại KCB : ${item.MA_LOAI_KCB} `}>
                <div className="whitespace-normal">
                  <Tag>{item.MA_LOAI_KCB}</Tag>
                  <div>{item.description}</div>
                </div>
              </Select.Option>
            ))}
          </Select>
        </div>
      </div>

      <Table
        bordered
        size="small"
        className="custom-table"
        rowClassName={(r) => {
          if (!checkValidInvoiceRow(r)) {
            return 'highlight-row-red'
          }

          return ''
        }}
        scroll={{
          x: invoiceCols.map((c) => c.width).reduce((a, b) => a + b) + 100,
          y: '70vh',
        }}
        rowSelection={{
          type: 'checkbox', // Checkbox selection
          selectedRowKeys: selectedInsuranceInvoiceRowKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedInsuranceInvoiceRowKeys(selectedRowKeys)
          },
          getCheckboxProps: (record) => ({
            //disabled: !!record.children, // Enable checkbox only if the row has children
            disabled: !record.children, // Disable checkbox for rows with children property (parent rows)
            style: { display: !record.children && 'none' },
          }),
          hideDefaultSelections: false,
          fixed: true,
        }}
        components={{ header: { cell: ResizableTitle } }}
        columns={[...makeResizableColumns(invoiceCols, setInvoiceCols, selectedVisitsInvoiceData)]}
        dataSource={selectedVisitsInvoiceData.filter(
          (r) =>
            r.MA_LOAI_KCB === filterMedicalTreatmentType || isEmpty(filterMedicalTreatmentType),
        )}
        pagination={false}
        expandable={{ expandRowByClick: true }}
      />

      <div className="row mt-2">
        <div className="d-flex justify-content-end gap-2">
          {isDebugMode && (
            <Dropdown
              trigger={['click']}
              menu={{
                items: [
                  {
                    key: '1',
                    label: 'Debug invoice',
                    onClick: () => {
                      logDebug(selectedInvoiceRows)
                    },
                  },
                ],
              }}>
              <Button>Debug</Button>
            </Dropdown>
          )}

          <Button
            icon={<i className="fas fa-sync-alt"></i>}
            onClick={async () => {
              await getInitData(true)
            }}>
            Refresh
          </Button>

          <AsyncButton
            hidden={modeViewData === MODE_VIEW_DATA.CASHIER}
            disabled={selectedInsuranceInvoiceRowKeys.length === 0 || defaultDisabled}
            onClick={handleValidateBeforeCreateXML}
            icon={<i className="fa-solid fa-table"></i>}
            variant="solid"
            color="cyan">
            Tạo bảng XML
          </AsyncButton>

          <AsyncButton
            variant="solid"
            color="green"
            icon={<i className="fa-solid fa-file-lines"></i>}
            disabled={selectedInsuranceInvoiceRowKeys.length === 0}
            onClick={handlePrintInvoice}>
            In bảng kê
          </AsyncButton>

          <AsyncButton
            hidden={
              modeViewData !== MODE_VIEW_DATA.CASHIER ||
              !checkPermission(PERMISSION.CASHIER_CONFIRM) ||
              mainVisit?.processing_status !== PROCESSING_STATUS.WAITING_CASHIER.name_e
            }
            disabled={!hasInvoices() || isCashierAlreadyConfirmed()}
            title={
              !hasInvoices()
                ? 'Không có hoá đơn để xác nhận'
                : isCashierAlreadyConfirmed()
                  ? `Đã xác nhận lượt khám này`
                  : ''
            }
            variant="solid"
            color="green"
            icon={<i className="fa-solid fa-check-circle"></i>}
            onClick={handleCashierConfirm}>
            Cashier Xác nhận
          </AsyncButton>

          <Popover content="Xác nhận đã kiểm tra thông tin và tự động tạo XML (không cần chọn thử công hóa đơn)">
            <AsyncButton
              hidden={
                modeViewData !== MODE_VIEW_DATA.NORMAL ||
                !checkPermission(PERMISSION.SIO_CONFIRM) ||
                mainVisit?.processing_status !== PROCESSING_STATUS.WAITING_TO_CREATE_XML.name_e
              }
              variant="solid"
              color="green"
              icon={<i className="fa-solid fa-check-circle"></i>}
              onClick={handleSIOConfirm}>
              SIO xác nhận
            </AsyncButton>
          </Popover>
        </div>
      </div>
    </div>
  )
}

InsuranceInvoicesTab.propTypes = propTypes

export default InsuranceInvoicesTab
